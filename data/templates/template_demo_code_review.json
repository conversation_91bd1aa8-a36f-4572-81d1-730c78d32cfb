{"id": "template_demo_code_review", "name": "代码评审检查单", "type": "code-review", "version": "1.0", "createdTime": "2025-01-01 10:00:00", "updatedTime": "2025-08-02 22:09:07", "description": null, "category": null, "tags": [], "isActive": null, "items": [{"id": "item_001", "sequence": 1, "content": "代码是否遵循团队编码规范？", "required": true, "category": "代码规范"}, {"id": "item_002", "sequence": 2, "content": "是否有适当的注释和文档？", "required": true, "category": "文档"}, {"id": "item_003", "sequence": 3, "content": "单元测试覆盖率是否达标？", "required": true, "category": "测试"}, {"id": "item_004", "sequence": 4, "content": "是否存在潜在的安全漏洞？", "required": true, "category": "安全"}, {"id": "item_005", "sequence": 5, "content": "代码性能是否满足要求？", "required": false, "category": "性能"}, {"id": "item_006", "sequence": 6, "content": "是否有重复代码需要重构？", "required": false, "category": "重构"}, {"id": "item_007", "sequence": 7, "content": "异常处理是否完善？", "required": true, "category": "异常处理"}, {"id": "item_008", "sequence": 8, "content": "数据库操作是否安全？", "required": true, "category": "数据库"}], "tableConfig": {"id": "default", "name": "默认视图", "columns": [{"id": "selection", "key": "selection", "label": "选择", "width": 60, "type": "custom", "visible": true, "fixed": true, "sortable": false}, {"id": "sequence", "key": "sequence", "label": "序号", "width": 80, "type": "number", "visible": true, "fixed": true, "sortable": true}, {"id": "content", "key": "content", "label": "检查内容", "minWidth": 300, "type": "text", "visible": true, "fixed": true, "sortable": false}, {"id": "status", "key": "status", "label": "状态", "width": 120, "type": "status", "visible": true, "fixed": true, "sortable": true}, {"id": "category", "key": "category", "label": "分类", "width": 120, "type": "tag", "visible": true, "sortable": true}, {"id": "reviewer", "key": "reviewer", "label": "评审人", "width": 120, "type": "text", "visible": true, "sortable": true}, {"id": "reviewTime", "key": "reviewTime", "label": "评审时间", "width": 160, "type": "date", "visible": true, "sortable": true}, {"id": "comment", "key": "comment", "label": "备注", "minWidth": 200, "type": "text", "visible": false, "sortable": false}], "groupBy": "category", "showGroupSummary": true, "defaultCollapsed": false}, "defectRules": [{"id": "rule_1754143734107", "name": "test", "description": "测试", "enabled": true, "trigger": {"status": ["FAIL"], "conditions": []}, "template": {"titleTemplate": "{category} - {content}", "descriptionTemplate": "检查项：{content}\n分类：{category}\n评审人：{reviewer}\n状态：不通过", "severityMapping": {}, "categoryMapping": {}, "customFieldMapping": {}}, "options": {"autoGenerate": false, "requireConfirmation": true, "batchGenerate": true}}], "statusButtons": [{"id": "group_1754143746354", "name": "默认", "description": "1", "layout": "horizontal", "buttons": [{"id": "btn_1754143746354_PASS", "label": "通过", "status": "PASS", "type": "success", "enabled": true, "order": 1, "action": {"apiEndpoint": "/api/review/items/{id}/status", "method": "PUT", "requireComment": false, "confirmMessage": "确定要将状态设置为\"通过\"吗？", "successMessage": "状态已更新为\"通过\"", "payloadTemplate": {"status": "PASS", "comment": "{comment}", "reviewer": "{reviewer}"}}, "displayConditions": {"currentStatus": ["PENDING"]}}], "defaultButtons": ["PASS"]}]}