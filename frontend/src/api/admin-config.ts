import request, { type RequestConfig } from '@/utils/request'
import {
  type StatusButtonGroup,
  type CustomStatusButton,
  type DefectRule
} from '@/types/defect-config'

/**
 * 后台配置管理API
 */
class AdminConfigApiService {
  private baseUrl = '/admin/config'

  /**
   * 获取模板的状态按钮组配置
   */
  async getTemplateButtonGroups(templateId: string, config?: RequestConfig): Promise<StatusButtonGroup[]> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.get(`${this.baseUrl}/templates/${templateId}/button-groups`, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 保存模板的状态按钮组配置
   */
  async saveTemplateButtonGroups(
    templateId: string, 
    buttonGroups: StatusButtonGroup[], 
    config?: RequestConfig
  ): Promise<void> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }
    if (!Array.isArray(buttonGroups)) {
      throw new Error('按钮组配置不能为空')
    }

    return request.put(`${this.baseUrl}/templates/${templateId}/button-groups`, {
      buttonGroups
    }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '按钮组配置保存成功',
      ...config,
    })
  }

  /**
   * 获取单个状态按钮组
   */
  async getButtonGroup(groupId: string, config?: RequestConfig): Promise<StatusButtonGroup> {
    if (!groupId?.trim()) {
      throw new Error('按钮组ID不能为空')
    }

    return request.get(`${this.baseUrl}/button-groups/${groupId}`, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 创建状态按钮组
   */
  async createButtonGroup(buttonGroup: Omit<StatusButtonGroup, 'id'>, config?: RequestConfig): Promise<StatusButtonGroup> {
    return request.post(`${this.baseUrl}/button-groups`, buttonGroup, {
      showLoading: true,
      showSuccess: true,
      successMessage: '按钮组创建成功',
      ...config,
    })
  }

  /**
   * 更新状态按钮组
   */
  async updateButtonGroup(groupId: string, buttonGroup: StatusButtonGroup, config?: RequestConfig): Promise<StatusButtonGroup> {
    if (!groupId?.trim()) {
      throw new Error('按钮组ID不能为空')
    }

    return request.put(`${this.baseUrl}/button-groups/${groupId}`, buttonGroup, {
      showLoading: true,
      showSuccess: true,
      successMessage: '按钮组更新成功',
      ...config,
    })
  }

  /**
   * 删除状态按钮组
   */
  async deleteButtonGroup(groupId: string, config?: RequestConfig): Promise<void> {
    if (!groupId?.trim()) {
      throw new Error('按钮组ID不能为空')
    }

    return request.delete(`${this.baseUrl}/button-groups/${groupId}`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '按钮组删除成功',
      ...config,
    })
  }

  /**
   * 获取模板的缺陷规则配置
   */
  async getTemplateDefectRules(templateId: string, config?: RequestConfig): Promise<DefectRule[]> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.get(`${this.baseUrl}/templates/${templateId}/defect-rules`, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 保存模板的缺陷规则配置
   */
  async saveTemplateDefectRules(
    templateId: string, 
    defectRules: DefectRule[], 
    config?: RequestConfig
  ): Promise<void> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }
    if (!Array.isArray(defectRules)) {
      throw new Error('缺陷规则配置不能为空')
    }

    return request.put(`${this.baseUrl}/templates/${templateId}/defect-rules`, {
      defectRules
    }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '缺陷规则配置保存成功',
      ...config,
    })
  }

  /**
   * 获取单个缺陷规则
   */
  async getDefectRule(ruleId: string, config?: RequestConfig): Promise<DefectRule> {
    if (!ruleId?.trim()) {
      throw new Error('规则ID不能为空')
    }

    return request.get(`${this.baseUrl}/defect-rules/${ruleId}`, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 创建缺陷规则
   */
  async createDefectRule(defectRule: Omit<DefectRule, 'id'>, config?: RequestConfig): Promise<DefectRule> {
    return request.post(`${this.baseUrl}/defect-rules`, defectRule, {
      showLoading: true,
      showSuccess: true,
      successMessage: '缺陷规则创建成功',
      ...config,
    })
  }

  /**
   * 更新缺陷规则
   */
  async updateDefectRule(ruleId: string, defectRule: DefectRule, config?: RequestConfig): Promise<DefectRule> {
    if (!ruleId?.trim()) {
      throw new Error('规则ID不能为空')
    }

    return request.put(`${this.baseUrl}/defect-rules/${ruleId}`, defectRule, {
      showLoading: true,
      showSuccess: true,
      successMessage: '缺陷规则更新成功',
      ...config,
    })
  }

  /**
   * 删除缺陷规则
   */
  async deleteDefectRule(ruleId: string, config?: RequestConfig): Promise<void> {
    if (!ruleId?.trim()) {
      throw new Error('规则ID不能为空')
    }

    return request.delete(`${this.baseUrl}/defect-rules/${ruleId}`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '缺陷规则删除成功',
      ...config,
    })
  }

  /**
   * 测试缺陷规则
   */
  async testDefectRule(
    ruleId: string, 
    testData: {
      content: string
      category: string
      status: string
      comment: string
      customFields?: Record<string, any>
    },
    config?: RequestConfig
  ): Promise<{
    matched: boolean
    generatedDefect?: {
      title: string
      description: string
      severity: string
      category: string
    }
    error?: string
  }> {
    if (!ruleId?.trim()) {
      throw new Error('规则ID不能为空')
    }

    return request.post(`${this.baseUrl}/defect-rules/${ruleId}/test`, testData, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * 获取所有可用的API接口列表（用于按钮配置）
   */
  async getAvailableApiEndpoints(config?: RequestConfig): Promise<{
    endpoint: string
    method: string
    description: string
    parameters: Array<{
      name: string
      type: string
      required: boolean
      description: string
    }>
  }[]> {
    return request.get(`${this.baseUrl}/api-endpoints`, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 获取模板的完整配置（包括按钮组和缺陷规则）
   */
  async getTemplateConfig(templateId: string, config?: RequestConfig): Promise<{
    buttonGroups: StatusButtonGroup[]
    defectRules: DefectRule[]
    customFields: Array<{
      key: string
      label: string
      type: string
      options?: string[]
    }>
  }> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    // 调用模板详情接口获取完整配置
    const response = await request.get(`/templates/detail/${templateId}?includeConfig=true`, {
      showLoading: false,
      ...config,
    })

    // 转换后端返回的数据格式为前端期望的格式
    return {
      buttonGroups: response.statusButtons || [],
      defectRules: response.defectRules || [],
      customFields: []  // 暂时返回空数组，后续可以从模板中提取
    }
  }

  /**
   * 保存模板的完整配置
   */
  async saveTemplateConfig(
    templateId: string,
    config: {
      buttonGroups: StatusButtonGroup[]
      defectRules: DefectRule[]
      customFields?: Array<{
        key: string
        label: string
        type: string
        options?: string[]
      }>
    },
    requestConfig?: RequestConfig
  ): Promise<void> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.put(`${this.baseUrl}/templates/${templateId}/config`, config, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板配置保存成功',
      ...requestConfig,
    })
  }

  /**
   * 复制模板配置到其他模板
   */
  async copyTemplateConfig(
    sourceTemplateId: string,
    targetTemplateIds: string[],
    options: {
      copyButtonGroups: boolean
      copyDefectRules: boolean
      copyCustomFields: boolean
    },
    config?: RequestConfig
  ): Promise<void> {
    if (!sourceTemplateId?.trim()) {
      throw new Error('源模板ID不能为空')
    }
    if (!Array.isArray(targetTemplateIds) || targetTemplateIds.length === 0) {
      throw new Error('目标模板ID不能为空')
    }

    return request.post(`${this.baseUrl}/templates/${sourceTemplateId}/copy-config`, {
      targetTemplateIds,
      options
    }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '配置复制成功',
      ...config,
    })
  }

  /**
   * 导出模板配置
   */
  async exportTemplateConfig(templateId: string, config?: RequestConfig): Promise<Blob> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.get(`${this.baseUrl}/templates/${templateId}/export`, {
      showLoading: true,
      responseType: 'blob',
      ...config,
    }) as Promise<Blob>
  }

  /**
   * 导入模板配置
   */
  async importTemplateConfig(
    templateId: string,
    configFile: File,
    options: {
      overwriteExisting: boolean
      mergeMode: 'replace' | 'merge' | 'append'
    },
    config?: RequestConfig
  ): Promise<{
    imported: {
      buttonGroups: number
      defectRules: number
      customFields: number
    }
    errors: string[]
  }> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }
    if (!configFile) {
      throw new Error('配置文件不能为空')
    }

    const formData = new FormData()
    formData.append('config', configFile)
    formData.append('options', JSON.stringify(options))

    return request.post(`${this.baseUrl}/templates/${templateId}/import`, formData, {
      showLoading: true,
      showSuccess: true,
      successMessage: '配置导入成功',
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config,
    })
  }

  /**
   * 验证模板配置
   */
  async validateTemplateConfig(
    templateId: string,
    config: {
      buttonGroups: StatusButtonGroup[]
      defectRules: DefectRule[]
    },
    requestConfig?: RequestConfig
  ): Promise<{
    valid: boolean
    errors: Array<{
      type: 'button' | 'rule' | 'general'
      id: string
      message: string
      severity: 'error' | 'warning'
    }>
    warnings: Array<{
      type: 'button' | 'rule' | 'general'
      id: string
      message: string
    }>
  }> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.post(`${this.baseUrl}/templates/${templateId}/validate`, config, {
      showLoading: true,
      ...requestConfig,
    })
  }
}

// Create and export service instance
const adminConfigApi = new AdminConfigApiService()

// Export individual methods for convenience with proper this binding
export const getTemplateButtonGroups = adminConfigApi.getTemplateButtonGroups.bind(adminConfigApi)
export const saveTemplateButtonGroups = adminConfigApi.saveTemplateButtonGroups.bind(adminConfigApi)
export const getButtonGroup = adminConfigApi.getButtonGroup.bind(adminConfigApi)
export const createButtonGroup = adminConfigApi.createButtonGroup.bind(adminConfigApi)
export const updateButtonGroup = adminConfigApi.updateButtonGroup.bind(adminConfigApi)
export const deleteButtonGroup = adminConfigApi.deleteButtonGroup.bind(adminConfigApi)
export const getTemplateDefectRules = adminConfigApi.getTemplateDefectRules.bind(adminConfigApi)
export const saveTemplateDefectRules = adminConfigApi.saveTemplateDefectRules.bind(adminConfigApi)
export const getDefectRule = adminConfigApi.getDefectRule.bind(adminConfigApi)
export const createDefectRule = adminConfigApi.createDefectRule.bind(adminConfigApi)
export const updateDefectRule = adminConfigApi.updateDefectRule.bind(adminConfigApi)
export const deleteDefectRule = adminConfigApi.deleteDefectRule.bind(adminConfigApi)
export const testDefectRule = adminConfigApi.testDefectRule.bind(adminConfigApi)
export const getAvailableApiEndpoints = adminConfigApi.getAvailableApiEndpoints.bind(adminConfigApi)
export const getTemplateConfig = adminConfigApi.getTemplateConfig.bind(adminConfigApi)
export const saveTemplateConfig = adminConfigApi.saveTemplateConfig.bind(adminConfigApi)
export const copyTemplateConfig = adminConfigApi.copyTemplateConfig.bind(adminConfigApi)
export const exportTemplateConfig = adminConfigApi.exportTemplateConfig.bind(adminConfigApi)
export const importTemplateConfig = adminConfigApi.importTemplateConfig.bind(adminConfigApi)
export const validateTemplateConfig = adminConfigApi.validateTemplateConfig.bind(adminConfigApi)

export default adminConfigApi

// Export utility functions
export function downloadConfigFile(blob: Blob, templateName: string) {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${templateName}_config_${new Date().toISOString().slice(0, 10)}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

export function validateButtonGroup(buttonGroup: StatusButtonGroup): string[] {
  const errors: string[] = []
  
  if (!buttonGroup.name?.trim()) {
    errors.push('按钮组名称不能为空')
  }
  
  if (!buttonGroup.buttons || buttonGroup.buttons.length === 0) {
    errors.push('按钮组至少需要包含一个按钮')
  }
  
  buttonGroup.buttons?.forEach((button, index) => {
    if (!button.label?.trim()) {
      errors.push(`第${index + 1}个按钮的标签不能为空`)
    }
    
    if (!button.status?.trim()) {
      errors.push(`第${index + 1}个按钮的状态值不能为空`)
    }
    
    if (!button.action?.apiEndpoint?.trim()) {
      errors.push(`第${index + 1}个按钮的API接口不能为空`)
    }
  })
  
  return errors
}

export function validateDefectRule(defectRule: DefectRule): string[] {
  const errors: string[] = []
  
  if (!defectRule.name?.trim()) {
    errors.push('规则名称不能为空')
  }
  
  if (!defectRule.trigger?.status || defectRule.trigger.status.length === 0) {
    errors.push('触发状态不能为空')
  }
  
  if (!defectRule.template?.titleTemplate?.trim()) {
    errors.push('标题模板不能为空')
  }
  
  if (!defectRule.template?.descriptionTemplate?.trim()) {
    errors.push('描述模板不能为空')
  }
  
  return errors
}
