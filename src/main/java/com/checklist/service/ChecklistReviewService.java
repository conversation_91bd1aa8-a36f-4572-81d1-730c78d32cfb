package com.checklist.service;

import com.checklist.model.*;
import com.checklist.repository.ChecklistReviewRepository;
import com.checklist.repository.ChecklistTemplateRepository;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 检查单评审服务类
 * 提供评审实例管理的业务逻辑，包括创建、更新、查询和历史记录功能
 */
public class ChecklistReviewService {
    
    private final ChecklistReviewRepository reviewRepository;
    private final ChecklistTemplateRepository templateRepository;
    
    public ChecklistReviewService(ChecklistReviewRepository reviewRepository, 
                                ChecklistTemplateRepository templateRepository) {
        this.reviewRepository = reviewRepository;
        this.templateRepository = templateRepository;
    }
    
    /**
     * 基于模板创建评审实例
     * 
     * @param templateId 模板ID
     * @return 创建的评审实例
     * @throws IOException IO异常
     * @throws IllegalArgumentException 参数验证异常
     */
    public ChecklistReview createReviewFromTemplate(String templateId) throws IOException {
        if (templateId == null || templateId.trim().isEmpty()) {
            throw new IllegalArgumentException("模板ID不能为空");
        }
        
        // 获取模板
        Optional<ChecklistTemplate> templateOpt = templateRepository.findById(templateId);
        if (!templateOpt.isPresent()) {
            throw new IllegalArgumentException("模板不存在: " + templateId);
        }
        
        ChecklistTemplate template = templateOpt.get();
        
        // 创建评审实例
        ChecklistReview review = new ChecklistReview();
        review.setTemplateId(templateId);
        review.setTemplateVersion(template.getVersion());
        review.setType(template.getType());
        review.setCreatedTime(LocalDateTime.now());
        review.setStatus("IN_PROGRESS");
        
        // 基于模板创建评审项
        List<ReviewItem> reviewItems = new ArrayList<>();
        if (template.getItems() != null) {
            for (ChecklistItem item : template.getItems()) {
                ReviewItem reviewItem = new ReviewItem();
                reviewItem.setItemId(item.getId());
                reviewItem.setSequence(item.getSequence());
                reviewItem.setContent(item.getContent());
                reviewItem.setCategory(item.getCategory());
                reviewItem.setRequired(item.getRequired());
                reviewItem.setStatus(ReviewStatus.PENDING);
                reviewItem.setComment("");
                reviewItem.setReviewHistory(new ArrayList<>());
                reviewItems.add(reviewItem);
            }
        }
        review.setReviewItems(reviewItems);
        
        // 保存评审实例
        return reviewRepository.save(review);
    }
    
    /**
     * 更新评审项状态
     * 
     * @param reviewId 评审ID
     * @param itemId 评审项ID
     * @param status 新状态
     * @param comment 评审意见
     * @param reviewer 评审人
     * @return 更新后的评审实例
     * @throws IOException IO异常
     * @throws IllegalArgumentException 参数验证异常
     */
    public ChecklistReview updateReviewItemStatus(String reviewId, String itemId, 
                                                ReviewStatus status, String comment, 
                                                String reviewer) throws IOException {
        // 参数验证
        if (reviewId == null || reviewId.trim().isEmpty()) {
            throw new IllegalArgumentException("评审ID不能为空");
        }
        if (itemId == null || itemId.trim().isEmpty()) {
            throw new IllegalArgumentException("评审项ID不能为空");
        }
        if (status == null) {
            throw new IllegalArgumentException("评审状态不能为空");
        }
        if (reviewer == null || reviewer.trim().isEmpty()) {
            throw new IllegalArgumentException("评审人不能为空");
        }
        
        // 获取评审实例
        Optional<ChecklistReview> reviewOpt = reviewRepository.findById(reviewId);
        if (!reviewOpt.isPresent()) {
            throw new IllegalArgumentException("评审实例不存在: " + reviewId);
        }
        
        ChecklistReview review = reviewOpt.get();
        
        // 查找对应的评审项
        ReviewItem targetItem = null;
        if (review.getReviewItems() != null) {
            for (ReviewItem item : review.getReviewItems()) {
                if (itemId.equals(item.getItemId())) {
                    targetItem = item;
                    break;
                }
            }
        }
        
        if (targetItem == null) {
            throw new IllegalArgumentException("评审项不存在: " + itemId);
        }
        
        // 创建评审记录
        ReviewRecord record = new ReviewRecord();
        record.setReviewer(reviewer);
        record.setReviewTime(LocalDateTime.now());
        record.setStatus(status);
        record.setComment(comment != null ? comment : "");
        
        // 更新评审项
        targetItem.setStatus(status);
        targetItem.setComment(comment != null ? comment : "");
        
        // 添加到历史记录
        if (targetItem.getReviewHistory() == null) {
            targetItem.setReviewHistory(new ArrayList<>());
        }
        targetItem.getReviewHistory().add(record);
        
        // 更新整体评审状态
        updateOverallReviewStatus(review);
        
        // 保存更新后的评审实例
        return reviewRepository.save(review);
    }
    
    /**
     * 批量更新评审项状态
     * 
     * @param reviewId 评审ID
     * @param itemIds 评审项ID列表
     * @param status 新状态
     * @param comment 评审意见
     * @param reviewer 评审人
     * @return 更新后的评审实例
     * @throws IOException IO异常
     * @throws IllegalArgumentException 参数验证异常
     */
    public ChecklistReview batchUpdateReviewItemStatus(String reviewId, List<String> itemIds, 
                                                     ReviewStatus status, String comment, 
                                                     String reviewer) throws IOException {
        // 参数验证
        if (reviewId == null || reviewId.trim().isEmpty()) {
            throw new IllegalArgumentException("评审ID不能为空");
        }
        if (itemIds == null || itemIds.isEmpty()) {
            throw new IllegalArgumentException("评审项ID列表不能为空");
        }
        if (status == null) {
            throw new IllegalArgumentException("评审状态不能为空");
        }
        if (reviewer == null || reviewer.trim().isEmpty()) {
            throw new IllegalArgumentException("评审人不能为空");
        }
        
        // 获取评审实例
        Optional<ChecklistReview> reviewOpt = reviewRepository.findById(reviewId);
        if (!reviewOpt.isPresent()) {
            throw new IllegalArgumentException("评审实例不存在: " + reviewId);
        }
        
        ChecklistReview review = reviewOpt.get();
        LocalDateTime now = LocalDateTime.now();
        
        // 批量更新评审项
        if (review.getReviewItems() != null) {
            for (ReviewItem item : review.getReviewItems()) {
                if (itemIds.contains(item.getItemId())) {
                    // 创建评审记录
                    ReviewRecord record = new ReviewRecord();
                    record.setReviewer(reviewer);
                    record.setReviewTime(now);
                    record.setStatus(status);
                    record.setComment(comment != null ? comment : "");
                    
                    // 更新评审项
                    item.setStatus(status);
                    item.setComment(comment != null ? comment : "");
                    
                    // 添加到历史记录
                    if (item.getReviewHistory() == null) {
                        item.setReviewHistory(new ArrayList<>());
                    }
                    item.getReviewHistory().add(record);
                }
            }
        }
        
        // 更新整体评审状态
        updateOverallReviewStatus(review);
        
        // 保存更新后的评审实例
        return reviewRepository.save(review);
    }
    
    /**
     * 获取评审实例
     * 
     * @param reviewId 评审ID
     * @return 评审实例的Optional包装
     * @throws IOException IO异常
     */
    public Optional<ChecklistReview> getReviewById(String reviewId) throws IOException {
        if (reviewId == null || reviewId.trim().isEmpty()) {
            return Optional.empty();
        }
        
        return reviewRepository.findById(reviewId);
    }
    
    /**
     * 获取评审历史记录
     * 
     * @param reviewId 评审ID
     * @return 评审历史记录列表
     * @throws IOException IO异常
     */
    public List<ReviewRecord> getReviewHistory(String reviewId) throws IOException {
        if (reviewId == null || reviewId.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        Optional<ChecklistReview> reviewOpt = reviewRepository.findById(reviewId);
        if (!reviewOpt.isPresent()) {
            return new ArrayList<>();
        }
        
        ChecklistReview review = reviewOpt.get();
        List<ReviewRecord> allRecords = new ArrayList<>();
        
        if (review.getReviewItems() != null) {
            for (ReviewItem item : review.getReviewItems()) {
                if (item.getReviewHistory() != null) {
                    allRecords.addAll(item.getReviewHistory());
                }
            }
        }
        
        // 按时间倒序排序
        allRecords.sort((r1, r2) -> {
            if (r1.getReviewTime() == null && r2.getReviewTime() == null) {
                return 0;
            }
            if (r1.getReviewTime() == null) {
                return 1;
            }
            if (r2.getReviewTime() == null) {
                return -1;
            }
            return r2.getReviewTime().compareTo(r1.getReviewTime());
        });
        
        return allRecords;
    }
    
    /**
     * 获取评审项的历史记录
     * 
     * @param reviewId 评审ID
     * @param itemId 评审项ID
     * @return 评审项历史记录列表
     * @throws IOException IO异常
     */
    public List<ReviewRecord> getReviewItemHistory(String reviewId, String itemId) throws IOException {
        if (reviewId == null || reviewId.trim().isEmpty() || 
            itemId == null || itemId.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        Optional<ChecklistReview> reviewOpt = reviewRepository.findById(reviewId);
        if (!reviewOpt.isPresent()) {
            return new ArrayList<>();
        }
        
        ChecklistReview review = reviewOpt.get();
        
        if (review.getReviewItems() != null) {
            for (ReviewItem item : review.getReviewItems()) {
                if (itemId.equals(item.getItemId())) {
                    List<ReviewRecord> history = item.getReviewHistory();
                    if (history != null) {
                        // 按时间倒序排序
                        history.sort((r1, r2) -> {
                            if (r1.getReviewTime() == null && r2.getReviewTime() == null) {
                                return 0;
                            }
                            if (r1.getReviewTime() == null) {
                                return 1;
                            }
                            if (r2.getReviewTime() == null) {
                                return -1;
                            }
                            return r2.getReviewTime().compareTo(r1.getReviewTime());
                        });
                        return new ArrayList<>(history);
                    }
                    break;
                }
            }
        }
        
        return new ArrayList<>();
    }
    
    /**
     * 根据类型获取评审列表
     * 
     * @param type 评审类型
     * @return 评审列表
     * @throws IOException IO异常
     */
    public List<ChecklistReview> getReviewsByType(String type) throws IOException {
        if (type == null || type.trim().isEmpty()) {
            throw new IllegalArgumentException("评审类型不能为空");
        }
        
        return reviewRepository.findByType(type);
    }
    
    /**
     * 根据状态获取评审列表
     * 
     * @param status 评审状态
     * @return 评审列表
     * @throws IOException IO异常
     */
    public List<ChecklistReview> getReviewsByStatus(String status) throws IOException {
        if (status == null || status.trim().isEmpty()) {
            throw new IllegalArgumentException("评审状态不能为空");
        }
        
        return reviewRepository.findByStatus(status);
    }
    
    /**
     * 获取最近的评审列表
     * 
     * @param limit 限制数量
     * @return 最近的评审列表
     * @throws IOException IO异常
     */
    public List<ChecklistReview> getRecentReviews(int limit) throws IOException {
        if (limit <= 0) {
            throw new IllegalArgumentException("限制数量必须大于0");
        }
        
        return reviewRepository.findRecentReviews(limit);
    }
    
    /**
     * 获取评审统计信息
     * 
     * @return 评审统计信息
     * @throws IOException IO异常
     */
    public Map<String, Object> getReviewStatistics() throws IOException {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总评审数量
        List<ChecklistReview> allReviews = reviewRepository.findAll();
        statistics.put("totalReviews", allReviews.size());
        
        // 按状态统计
        Map<String, Long> statusCounts = allReviews.stream()
            .collect(Collectors.groupingBy(
                review -> review.getStatus() != null ? review.getStatus() : "UNKNOWN",
                Collectors.counting()
            ));
        statistics.put("statusCounts", statusCounts);
        
        // 按类型统计
        Map<String, Long> typeCounts = allReviews.stream()
            .collect(Collectors.groupingBy(
                review -> review.getType() != null ? review.getType() : "UNKNOWN",
                Collectors.counting()
            ));
        statistics.put("typeCounts", typeCounts);
        
        // 今日创建的评审数量
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime todayEnd = todayStart.plusDays(1);
        
        long todayCount = allReviews.stream()
            .filter(review -> {
                LocalDateTime createdTime = review.getCreatedTime();
                return createdTime != null && 
                       !createdTime.isBefore(todayStart) && 
                       createdTime.isBefore(todayEnd);
            })
            .count();
        statistics.put("todayReviews", todayCount);
        
        return statistics;
    }
    
    /**
     * 删除评审实例
     * 
     * @param reviewId 评审ID
     * @throws IOException IO异常
     * @throws IllegalArgumentException 参数验证异常
     */
    public void deleteReview(String reviewId) throws IOException {
        if (reviewId == null || reviewId.trim().isEmpty()) {
            throw new IllegalArgumentException("评审ID不能为空");
        }
        
        if (!reviewRepository.existsById(reviewId)) {
            throw new IllegalArgumentException("评审实例不存在: " + reviewId);
        }
        
        reviewRepository.deleteById(reviewId);
    }
    
    /**
     * 检查评审是否存在冲突
     * 
     * @param reviewId 评审ID
     * @param itemId 评审项ID
     * @return 冲突的评审记录列表
     * @throws IOException IO异常
     */
    public List<ReviewRecord> checkReviewConflicts(String reviewId, String itemId) throws IOException {
        List<ReviewRecord> itemHistory = getReviewItemHistory(reviewId, itemId);
        
        if (itemHistory.size() <= 1) {
            return new ArrayList<>(); // 没有冲突
        }
        
        // 检查是否有不同的评审结果
        Set<ReviewStatus> statuses = itemHistory.stream()
            .map(ReviewRecord::getStatus)
            .collect(Collectors.toSet());
        
        if (statuses.size() > 1) {
            // 存在冲突，返回所有记录
            return itemHistory;
        }
        
        return new ArrayList<>(); // 没有冲突
    }
    
    /**
     * 更新整体评审状态
     * 
     * @param review 评审实例
     */
    private void updateOverallReviewStatus(ChecklistReview review) {
        if (review.getReviewItems() == null || review.getReviewItems().isEmpty()) {
            review.setStatus("EMPTY");
            return;
        }
        
        boolean hasFailedItems = false;
        boolean hasPendingItems = false;
        
        for (ReviewItem item : review.getReviewItems()) {
            ReviewStatus status = item.getStatus();
            if (status == ReviewStatus.FAIL) {
                hasFailedItems = true;
            } else if (status == ReviewStatus.PENDING) {
                hasPendingItems = true;
            }
        }
        
        if (hasFailedItems) {
            review.setStatus("FAILED");
        } else if (hasPendingItems) {
            review.setStatus("IN_PROGRESS");
        } else {
            review.setStatus("COMPLETED");
        }
    }
}