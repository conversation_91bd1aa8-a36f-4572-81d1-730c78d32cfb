<template>
  <div class="checklist-review">
    <!-- 优化后的顶部导航栏 -->
    <div class="review-header">
      <div class="header-content">
        <div class="header-left">
          <div class="title-section">
            <h1>{{ reviewType }}</h1>
            <p class="subtitle">检查单评审</p>
          </div>
        </div>

        <div class="header-right">
          <div class="status-section">
            <el-tag v-if="currentReview" :type="getStatusTagType(currentReview.status)" size="large" class="status-tag">
              {{ getStatusText(currentReview.status) }}
            </el-tag>
            <div class="auto-save-indicator" v-if="pendingChanges.size > 0">
              <el-icon class="saving-icon"><Clock /></el-icon>
              <span>自动保存中...</span>
            </div>
          </div>

          <div class="action-buttons">
            <el-button type="success" @click="saveReview" :loading="saving" class="save-btn">
              <el-icon><Check /></el-icon>
              保存评审
            </el-button>
            <el-button type="primary" @click="completeReview" :loading="completing" :disabled="!canComplete" class="complete-btn">
              <el-icon><CircleCheck /></el-icon>
              完成评审
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="main-content">
        <!-- Loading state -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="8" animated />
        </div>

        <!-- Error state -->
        <div v-else-if="error" class="error-container">
          <el-result icon="error" title="加载失败" :sub-title="error">
            <template #extra>
              <el-button type="primary" @click="loadReviewData">重新加载</el-button>
            </template>
          </el-result>
        </div>

        <!-- Review content -->
        <div v-else-if="currentReview" class="review-content">
          <!-- Version upgrade notification -->
          <div v-if="upgradeAvailable && upgradeInfo" class="version-upgrade-notice">
            <el-alert
              title="模板版本更新"
              :description="`检测到模板有新版本 ${upgradeInfo.latestVersion}，当前使用版本 ${upgradeInfo.currentVersion}`"
              type="info"
              show-icon
              :closable="false"
            >
              <template #default>
                <div class="upgrade-actions">
                  <el-button size="small" @click="showVersionUpgradeDialog = true">
                    查看变更
                  </el-button>
                  <el-button size="small" type="primary" @click="handleVersionUpgrade">
                    立即升级
                  </el-button>
                </div>
              </template>
            </el-alert>
          </div>

          <!-- 优化后的进度统计 -->
          <div class="progress-overview">
            <div class="progress-cards">
              <div class="progress-card total">
                <div class="card-icon">📋</div>
                <div class="card-content">
                  <div class="card-number">{{ currentReview.progress?.total || 0 }}</div>
                  <div class="card-label">总检查项</div>
                </div>
              </div>

              <div class="progress-card completed">
                <div class="card-icon">✅</div>
                <div class="card-content">
                  <div class="card-number">{{ currentReview.progress?.completed || 0 }}</div>
                  <div class="card-label">已完成</div>
                </div>
              </div>

              <div class="progress-card passed">
                <div class="card-icon">✔️</div>
                <div class="card-content">
                  <div class="card-number">{{ currentReview.progress?.passed || 0 }}</div>
                  <div class="card-label">通过</div>
                </div>
              </div>

              <div class="progress-card failed">
                <div class="card-icon">❌</div>
                <div class="card-content">
                  <div class="card-number">{{ currentReview.progress?.failed || 0 }}</div>
                  <div class="card-label">不通过</div>
                </div>
              </div>

              <div class="progress-card skipped">
                <div class="card-icon">⏭️</div>
                <div class="card-content">
                  <div class="card-number">{{ currentReview.progress?.skipped || 0 }}</div>
                  <div class="card-label">跳过</div>
                </div>
              </div>
            </div>

            <div class="progress-bar-section">
              <div class="progress-info">
                <span class="progress-text">完成进度</span>
                <span class="progress-percentage">{{ currentReview.progress?.percentage || 0 }}%</span>
              </div>
              <el-progress
                :percentage="currentReview.progress?.percentage || 0"
                :stroke-width="12"
                :color="getProgressColor(currentReview.progress?.percentage || 0)"
                :show-text="false"
                class="custom-progress"
              />
            </div>
          </div>

          <!-- 评审内容 Tab 页面 -->
          <ReviewTabs
            :review-id="currentReview.id"
            :filtered-items="filteredItems"
            :selected-items="selectedItems"
            :readonly="currentReview?.status === 'COMPLETED'"
            :view-mode="viewMode"
            :table-configs="tableConfigs"
            :current-table-config-id="currentTableConfigId"
            :current-table-config="currentTableConfig"
            :search-text="searchText"
            :status-filter="statusFilter"
            :button-group="buttonGroup"
            :defect-rules="defectRules"
            :defects="generatedDefects"
            :current-user="currentUser"
            :is-multi-review-mode="isMultiReviewMode"
            :collaborators="collaborators"
            @tab-change="handleTabChange"
            @mode-change="handleViewModeChange"
            @config-change="handleTableConfigChange"
            @table-config-change="handleTableConfigUpdate"
            @search-change="handleSearchChange"
            @status-filter-change="handleStatusFilterChange"
            @group-by-change="handleGroupByChange"
            @filter-command="handleFilterCommand"
            @item-select="onItemSelect"
            @item-status-change="onItemStatusChange"
            @item-comment-change="onItemCommentChange"
            @custom-status-change="handleCustomStatusChange"
            @defect-generated="handleDefectGenerated"
            @table-selection-change="handleTableSelectionChange"
            @table-sort-change="handleTableSortChange"
            @table-cell-change="handleTableCellChange"
            @export-data="handleExportData"
            @defect-confirm="handleDefectConfirm"
            @defect-submit="handleDefectSubmit"
            @defect-edit="handleDefectEdit"
            @defect-delete="handleDefectDelete"
            @defects-export="handleDefectsExport"
            @defects-clear="handleDefectsClear"
            @collaborator-add="handleCollaboratorAdd"
            @collaborator-remove="handleCollaboratorRemove"
            @assignment-change="handleAssignmentChange"
          />
        </div>

        <!-- No review state -->
        <div v-else class="no-review-container">
          <el-result icon="info" title="未找到评审数据" sub-title="请检查评审类型是否正确">
            <template #extra>
              <el-button type="primary" @click="createNewReview">创建新评审</el-button>
            </template>
          </el-result>
        </div>
    </div>

    <!-- Batch operations component -->
    <BatchOperations
      v-if="currentReview"
      :selected-items="selectedItems"
      :all-items="currentReview.reviewItems"
      @batch-update="onBatchUpdate"
      @update-selection="onUpdateSelection"
      @clear-selection="onClearSelection"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Check, CircleCheck, Clock } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  type ChecklistReview,
  type ReviewItem,
  ReviewStatus,
  ReviewItemStatus,
  getReview,
  createReview,
  updateReviewItem,
  batchUpdateReviewItems,
  completeReview as completeReviewApi,
  autoSaveReview
} from '@/api/review'
import { getTemplatesByType } from '@/api/template'
import {
  type ExtendedReviewItem,
  type TableViewConfig,
  type ExportConfig,
  ViewMode,
  DEFAULT_TABLE_CONFIG
} from '@/types/table-config'
import {
  type StatusButtonGroup,
  type DefectRule,
  type GeneratedDefect,
  type CustomStatusButton,
  DEFAULT_BUTTON_GROUP,
  DEFAULT_DEFECT_RULES
} from '@/types/defect-config'
import { getTemplateConfig } from '@/api/admin-config'
import {
  createReviewTemplateVersion,
  checkUpgradeAvailable
} from '@/api/template-version'
import ReviewItemComponent from '@/components/review/ReviewItem.vue'
import BatchOperations from '@/components/review/BatchOperations.vue'
import ViewModeSelector from '@/components/review/ViewModeSelector.vue'
import GroupedTableView from '@/components/review/GroupedTableView.vue'
import DefectManager from '@/components/review/DefectManager.vue'
import ReviewTabs from '@/components/review/ReviewTabs.vue'

const route = useRoute()
const router = useRouter()

// Reactive data
const loading = ref(true)
const saving = ref(false)
const completing = ref(false)
const error = ref('')
const currentReview = ref<ChecklistReview | null>(null)
const selectedItems = ref<string[]>([])
const searchText = ref('')
const statusFilter = ref('')

// New view mode and table configuration
const viewMode = ref<ViewMode>(ViewMode.CARD)
const tableConfigs = ref<TableViewConfig[]>([DEFAULT_TABLE_CONFIG])
const currentTableConfigId = ref('default')
const additionalFilters = ref<Record<string, any>>({})

// Defect management
const buttonGroup = ref<StatusButtonGroup>(DEFAULT_BUTTON_GROUP)
const defectRules = ref<DefectRule[]>(DEFAULT_DEFECT_RULES)
const generatedDefects = ref<GeneratedDefect[]>([])
const currentUser = ref('current-user')

// Version management
const upgradeAvailable = ref(false)
const upgradeInfo = ref<any>(null)
const showVersionUpgradeDialog = ref(false)

// Auto-save functionality
const autoSaveTimer = ref<ReturnType<typeof setTimeout> | null>(null)
const pendingChanges = ref(new Set<string>())

// Multi-reviewer functionality
const isMultiReviewMode = ref(false)
const collaborators = ref<any[]>([])
const currentTab = ref('checklist')

// Computed properties
const reviewType = computed(() => {
  return route.params.type as string
})

const reviewId = computed(() => {
  return route.params.id as string || route.query.reviewId as string
})

const extendedItems = computed((): ExtendedReviewItem[] => {
  if (!currentReview.value) return []

  return currentReview.value.reviewItems.map(item => ({
    ...item,
    customFields: item.customFields || {}
  }))
})

const currentTableConfig = computed(() => {
  return tableConfigs.value.find(config => config.id === currentTableConfigId.value)
    || DEFAULT_TABLE_CONFIG
})

const filteredItems = computed(() => {
  if (!currentReview.value) return []

  let items = extendedItems.value

  // Filter by search text
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    items = items.filter(item =>
      item.content.toLowerCase().includes(search) ||
      item.category.toLowerCase().includes(search)
    )
  }

  // Filter by status
  if (statusFilter.value) {
    items = items.filter(item => item.status === statusFilter.value)
  }

  // Apply additional filters
  if (additionalFilters.value.required) {
    items = items.filter(item => item.required)
  }

  if (additionalFilters.value.commented) {
    items = items.filter(item => item.comment && item.comment.trim())
  }

  if (additionalFilters.value.recent) {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
    items = items.filter(item =>
      item.reviewTime && new Date(item.reviewTime) > oneDayAgo
    )
  }

  return items.sort((a, b) => a.sequence - b.sequence)
})

const canComplete = computed(() => {
  if (!currentReview.value) return false
  
  // Check if all required items are reviewed
  const requiredItems = currentReview.value.reviewItems.filter(item => item.required)
  const reviewedRequiredItems = requiredItems.filter(item => item.status !== ReviewItemStatus.PENDING)
  
  return reviewedRequiredItems.length === requiredItems.length
})

// Methods
const loadReviewData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    if (reviewId.value) {
      // Load existing review
      currentReview.value = await getReview(reviewId.value)

      // Load template configuration
      if (currentReview.value.templateId) {
        await loadTemplateConfig(currentReview.value.templateId)

        // Check for version upgrades
        await checkVersionUpgrade(currentReview.value.id)
      }
    } else {
      // Create new review from template
      await createNewReview()
    }
  } catch (err: any) {
    error.value = err.message || '加载评审数据失败'
    console.error('Failed to load review data:', err)
  } finally {
    loading.value = false
  }
}

const createNewReview = async () => {
  try {
    // Get templates by type
    const templates = await getTemplatesByType(reviewType.value)
    if (templates.length === 0) {
      throw new Error(`未找到类型为 "${reviewType.value}" 的模板`)
    }
    
    // Use the first template (or let user choose if multiple)
    const template = templates[0]
    
    // Create new review
    const newReview = await createReview(template.id, reviewType.value)
    currentReview.value = newReview

    // Load template configuration
    await loadTemplateConfig(template.id)

    // 创建模板版本快照关联
    try {
      await createReviewTemplateVersion(newReview.id, template.id)
      console.log('Template version snapshot created for review:', newReview.id)
    } catch (error) {
      console.warn('Failed to create template version snapshot:', error)
      // 不阻塞主流程，只记录警告
    }

    // Update URL with review ID
    router.replace({
      name: route.name!,
      params: { ...route.params, id: newReview.id },
      query: route.query
    })

    ElMessage.success('评审实例创建成功')
  } catch (err: any) {
    error.value = err.message || '创建评审实例失败'
    throw err
  }
}

const onItemStatusChange = async (payload: { item: ReviewItem, status: ReviewItemStatus }) => {
  const { item, status } = payload
  try {
    // Update local item status
    item.status = status
    
    // Clear comment if not FAIL status
    if (status !== ReviewItemStatus.FAIL) {
      item.comment = ''
    }
    
    // Add to pending changes for auto-save
    pendingChanges.value.add(item.itemId)
    scheduleAutoSave()
    
    // Update item immediately
    await updateReviewItem(
      currentReview.value!.id,
      item.itemId,
      status,
      item.comment
    )
    
    // Update progress
    updateProgress()
    
    // Remove from pending changes
    pendingChanges.value.delete(item.itemId)
    
  } catch (err: any) {
    ElMessage.error(err.message || '更新评审项失败')
    console.error('Failed to update review item:', err)
  }
}

const onItemCommentChange = async (item: ReviewItem, comment: string) => {
  try {
    // Update local item comment
    item.comment = comment
    
    // Add to pending changes for auto-save
    pendingChanges.value.add(item.itemId)
    scheduleAutoSave()
    
    // Update item
    await updateReviewItem(
      currentReview.value!.id,
      item.itemId,
      item.status,
      comment
    )
    
    // Remove from pending changes
    pendingChanges.value.delete(item.itemId)
    
  } catch (err: any) {
    ElMessage.error(err.message || '更新评审项备注失败')
    console.error('Failed to update review item comment:', err)
  }
}

const onItemSelect = (payload: { itemId: string, selected: boolean }) => {
  const { itemId, selected } = payload
  if (selected) {
    if (!selectedItems.value.includes(itemId)) {
      selectedItems.value.push(itemId)
    }
  } else {
    const index = selectedItems.value.indexOf(itemId)
    if (index > -1) {
      selectedItems.value.splice(index, 1)
    }
  }
}



const updateProgress = () => {
  if (!currentReview.value) return
  
  const items = currentReview.value.reviewItems
  const total = items.length
  const passed = items.filter(item => item.status === ReviewItemStatus.PASS).length
  const failed = items.filter(item => item.status === ReviewItemStatus.FAIL).length
  const skipped = items.filter(item => item.status === ReviewItemStatus.SKIP).length
  const pending = items.filter(item => item.status === ReviewItemStatus.PENDING).length
  const completed = total - pending
  const percentage = total > 0 ? Math.round((completed / total) * 100) : 0
  
  currentReview.value.progress = {
    total,
    completed,
    passed,
    failed,
    skipped,
    pending,
    percentage
  }
}

const scheduleAutoSave = () => {
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value)
  }
  
  autoSaveTimer.value = setTimeout(async () => {
    if (pendingChanges.value.size > 0 && currentReview.value) {
      try {
        await autoSaveReview(currentReview.value.id, {
          reviewItems: currentReview.value.reviewItems,
          progress: currentReview.value.progress
        })
      } catch (err) {
        console.warn('Auto-save failed:', err)
      }
    }
  }, 2000) // Auto-save after 2 seconds of inactivity
}

const saveReview = async () => {
  if (!currentReview.value) return
  
  try {
    saving.value = true
    
    // Save all pending changes
    const promises = Array.from(pendingChanges.value).map(itemId => {
      const item = currentReview.value!.reviewItems.find(i => i.itemId === itemId)
      if (item) {
        return updateReviewItem(
          currentReview.value!.id,
          item.itemId,
          item.status,
          item.comment
        )
      }
    })
    
    await Promise.all(promises)
    pendingChanges.value.clear()
    
    ElMessage.success('评审已保存')
  } catch (err: any) {
    ElMessage.error(err.message || '保存评审失败')
    console.error('Failed to save review:', err)
  } finally {
    saving.value = false
  }
}

const completeReview = async () => {
  if (!currentReview.value || !canComplete.value) return
  
  try {
    await ElMessageBox.confirm(
      '确认完成评审？完成后将无法再修改评审结果。',
      '确认完成',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    completing.value = true
    
    // Save any pending changes first
    await saveReview()
    
    // Complete the review
    const completedReview = await completeReviewApi(currentReview.value.id)
    currentReview.value = completedReview
    
    ElMessage.success('评审已完成')
    
    // Redirect to review dashboard
    router.push('/review-dashboard')
    
  } catch (err: any) {
    if (err !== 'cancel') {
      ElMessage.error(err.message || '完成评审失败')
      console.error('Failed to complete review:', err)
    }
  } finally {
    completing.value = false
  }
}

// Utility methods
const getStatusText = (status: ReviewStatus | ReviewItemStatus) => {
  // Handle ReviewStatus
  if (Object.values(ReviewStatus).includes(status as ReviewStatus)) {
    const reviewStatusMap = {
      [ReviewStatus.PENDING]: '待开始',
      [ReviewStatus.IN_PROGRESS]: '进行中',
      [ReviewStatus.COMPLETED]: '已完成',
      [ReviewStatus.CANCELLED]: '已取消',
    }
    return reviewStatusMap[status as ReviewStatus] || status
  }
  
  // Handle ReviewItemStatus
  const itemStatusMap = {
    [ReviewItemStatus.PENDING]: '待处理',
    [ReviewItemStatus.PASS]: '通过',
    [ReviewItemStatus.FAIL]: '不通过',
    [ReviewItemStatus.SKIP]: '跳过',
  }
  return itemStatusMap[status as ReviewItemStatus] || status
}

const getStatusTagType = (status: ReviewStatus | ReviewItemStatus) => {
  // Handle ReviewStatus
  if (Object.values(ReviewStatus).includes(status as ReviewStatus)) {
    const reviewStatusTypeMap = {
      [ReviewStatus.PENDING]: 'info',
      [ReviewStatus.IN_PROGRESS]: 'warning',
      [ReviewStatus.COMPLETED]: 'success',
      [ReviewStatus.CANCELLED]: 'danger',
    }
    return reviewStatusTypeMap[status as ReviewStatus] || 'info'
  }
  
  // Handle ReviewItemStatus
  const itemStatusTypeMap = {
    [ReviewItemStatus.PENDING]: 'info',
    [ReviewItemStatus.PASS]: 'success',
    [ReviewItemStatus.FAIL]: 'danger',
    [ReviewItemStatus.SKIP]: 'warning',
  }
  return itemStatusTypeMap[status as ReviewItemStatus] || 'info'
}

const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

const onBatchUpdate = async (itemIds: string[], status: ReviewItemStatus, comment?: string) => {
  if (!currentReview.value) return
  
  try {
    // Update local items first
    const updatedItems = currentReview.value.reviewItems.filter(item => 
      itemIds.includes(item.itemId)
    )
    
    updatedItems.forEach(item => {
      item.status = status
      item.comment = comment || ''
    })
    
    // Perform batch update
    await batchUpdateReviewItems(currentReview.value.id, {
      itemIds,
      status,
      comment,
      reviewer: 'current-user' // This should come from auth context
    })
    
    // Update progress
    updateProgress()
    
  } catch (err: any) {
    ElMessage.error(err.message || '批量更新失败')
    console.error('Failed to batch update items:', err)
  }
}

const onUpdateSelection = (itemIds: string[]) => {
  selectedItems.value = [...itemIds]
}

const onClearSelection = () => {
  selectedItems.value = []
}

// New methods for view mode and table configuration
const handleViewModeChange = (mode: ViewMode) => {
  viewMode.value = mode
}

const handleTableConfigChange = (configId: string) => {
  currentTableConfigId.value = configId
}

const handleTableConfigUpdate = (config: TableViewConfig) => {
  const index = tableConfigs.value.findIndex(c => c.id === config.id)
  if (index >= 0) {
    tableConfigs.value[index] = config
  } else {
    tableConfigs.value.push(config)
  }

  // Save to localStorage
  localStorage.setItem('checklist-table-configs', JSON.stringify(tableConfigs.value))
}

const handleSearchChange = (text: string) => {
  searchText.value = text
}

const handleStatusFilterChange = (status: string) => {
  statusFilter.value = status
}

const handleGroupByChange = (field: string) => {
  const config = { ...currentTableConfig.value }
  config.groupBy = field || undefined
  handleTableConfigUpdate(config)
}

const handleFilterCommand = (command: string) => {
  switch (command) {
    case 'required':
      additionalFilters.value.required = !additionalFilters.value.required
      break
    case 'commented':
      additionalFilters.value.commented = !additionalFilters.value.commented
      break
    case 'recent':
      additionalFilters.value.recent = !additionalFilters.value.recent
      break
    case 'clear':
      additionalFilters.value = {}
      searchText.value = ''
      statusFilter.value = ''
      break
  }
}

const handleTableSelectionChange = (selectedIds: string[]) => {
  selectedItems.value = selectedIds
}

const handleTableSortChange = (sortBy: string, sortOrder: 'asc' | 'desc') => {
  const config = { ...currentTableConfig.value }
  config.sortBy = sortBy
  config.sortOrder = sortOrder
  handleTableConfigUpdate(config)
}

const handleTableCellChange = (itemId: string, field: string, value: any) => {
  const item = currentReview.value?.reviewItems.find(i => i.itemId === itemId)
  if (!item) return

  if (field.startsWith('customFields.')) {
    const customField = field.replace('customFields.', '')
    if (!item.customFields) {
      item.customFields = {}
    }
    item.customFields[customField] = value
  } else if (field === 'comment') {
    onItemCommentChange(item, value)
    return
  } else {
    (item as any)[field] = value
  }

  // Add to pending changes for auto-save
  pendingChanges.value.add(itemId)
  scheduleAutoSave()
}

const handleExportData = (config: ExportConfig) => {
  const itemsToExport = config.selectedOnly
    ? filteredItems.value.filter(item => selectedItems.value.includes(item.itemId))
    : filteredItems.value

  const exportData = itemsToExport.map(item => {
    const data: any = {
      序号: item.sequence,
      检查内容: item.content,
      状态: getStatusText(item.status),
      分类: item.category,
      评审人: item.reviewer || '',
      评审时间: item.reviewTime || '',
      备注: item.comment || ''
    }

    // Add custom fields if requested
    if (config.includeCustomFields && item.customFields) {
      Object.keys(item.customFields).forEach(key => {
        data[key] = item.customFields![key]
      })
    }

    return data
  })

  // Here you would implement the actual export logic
  // For now, just show a message
  ElMessage.success(`导出 ${exportData.length} 条数据`)
  console.log('Export data:', exportData)
}

// 处理自定义状态变更
const handleCustomStatusChange = async (button: CustomStatusButton, payload: any) => {
  try {
    // 这里调用对应的API接口
    const apiUrl = button.action.apiEndpoint.replace('{itemId}', payload.itemId || '')

    // 模拟API调用
    console.log(`Calling ${button.action.method} ${apiUrl}`, payload)

    // 更新本地状态
    const item = currentReview.value?.reviewItems.find(i => i.itemId === payload.itemId)
    if (item) {
      item.status = payload.status
      item.comment = payload.comment || item.comment
      item.reviewer = payload.reviewer || item.reviewer
      item.reviewTime = payload.reviewTime || item.reviewTime

      // 更新自定义字段
      if (payload.customFields) {
        if (!item.customFields) {
          item.customFields = {}
        }
        Object.assign(item.customFields, payload.customFields)
      }
    }

    // 更新进度
    updateProgress()

    // 添加到待保存列表
    if (item) {
      pendingChanges.value.add(item.itemId)
      scheduleAutoSave()
    }

  } catch (error: any) {
    ElMessage.error(error.message || '状态更新失败')
  }
}

// 处理缺陷生成
const handleDefectGenerated = (defect: GeneratedDefect) => {
  generatedDefects.value.push(defect)
  ElMessage.success('缺陷已生成')
}

// 缺陷管理方法
const handleDefectConfirm = (defect: GeneratedDefect) => {
  console.log('Defect confirmed:', defect)
  // 这里可以调用API确认缺陷
}

const handleDefectSubmit = (defect: GeneratedDefect) => {
  console.log('Defect submitted:', defect)
  // 这里可以调用API提交缺陷
}

const handleDefectEdit = (defect: GeneratedDefect) => {
  console.log('Defect edited:', defect)
  // 缺陷已在组件内部更新
}

const handleDefectDelete = (defectId: string) => {
  const index = generatedDefects.value.findIndex(d => d.id === defectId)
  if (index > -1) {
    generatedDefects.value.splice(index, 1)
  }
}

const handleDefectsExport = (defects: GeneratedDefect[]) => {
  // 导出缺陷数据
  const exportData = defects.map(defect => ({
    标题: defect.title,
    描述: defect.description,
    严重程度: defect.severity,
    分类: defect.category,
    源检查项: defect.sourceContent,
    创建时间: defect.createdTime,
    状态: defect.status,
    ...defect.customData
  }))

  console.log('Export defects:', exportData)
  ElMessage.success(`导出 ${exportData.length} 个缺陷`)
}

const handleDefectsClear = () => {
  generatedDefects.value = []
}

// Tab 相关方法
const handleTabChange = (tabName: string) => {
  currentTab.value = tabName
}

// 多人协作相关方法
const handleCollaboratorAdd = (collaborator: any) => {
  collaborators.value.push(collaborator)
  console.log('Collaborator added:', collaborator)
}

const handleCollaboratorRemove = (collaboratorId: string) => {
  const index = collaborators.value.findIndex(c => c.id === collaboratorId)
  if (index > -1) {
    collaborators.value.splice(index, 1)
  }
  console.log('Collaborator removed:', collaboratorId)
}

const handleAssignmentChange = (assignment: any) => {
  console.log('Assignment changed:', assignment)
  // 这里应该调用API更新任务分配
}

// 检查版本升级
const checkVersionUpgrade = async (reviewId: string) => {
  try {
    const result = await checkUpgradeAvailable(reviewId)
    if (result.upgradeAvailable) {
      upgradeAvailable.value = true
      upgradeInfo.value = result

      // 显示升级提示
      ElMessage.info({
        message: `检测到模板有新版本 ${result.latestVersion}，当前版本 ${result.currentVersion}`,
        duration: 5000,
        showClose: true
      })
    }
  } catch (error) {
    console.warn('Failed to check version upgrade:', error)
  }
}

// 处理版本升级
const handleVersionUpgrade = async () => {
  if (!upgradeInfo.value || !currentReview.value) return

  try {
    await ElMessageBox.confirm(
      '升级到新版本可能会修改检查项内容，已填写的数据将尽可能保留。确认升级？',
      '确认版本升级',
      { type: 'warning' }
    )

    // 这里可以调用升级API
    ElMessage.info('版本升级功能开发中...')

  } catch {
    // User cancelled
  }
}



// Lifecycle hooks
onMounted(() => {
  loadReviewData()
  loadTableConfigs()
})

// Load table configurations from localStorage
const loadTableConfigs = () => {
  try {
    const saved = localStorage.getItem('checklist-table-configs')
    if (saved) {
      const configs = JSON.parse(saved)
      if (Array.isArray(configs) && configs.length > 0) {
        tableConfigs.value = configs
      }
    }
  } catch (error) {
    console.warn('Failed to load table configs:', error)
  }
}

// Load template configuration from backend
const loadTemplateConfig = async (templateId: string) => {
  try {
    const config = await getTemplateConfig(templateId)

    // 更新按钮组配置
    if (config.buttonGroups && config.buttonGroups.length > 0) {
      buttonGroup.value = config.buttonGroups[0] // 使用第一个按钮组
    }

    // 更新缺陷规则配置
    if (config.defectRules && config.defectRules.length > 0) {
      defectRules.value = config.defectRules
    }

  } catch (error: any) {
    console.warn('Failed to load template config:', error)
    // 使用默认配置
    buttonGroup.value = DEFAULT_BUTTON_GROUP
    defectRules.value = DEFAULT_DEFECT_RULES
  }
}

// Watch for route changes
watch(() => route.params, () => {
  if (route.name === 'ChecklistReview') {
    loadReviewData()
  }
}, { deep: true })

// Cleanup
onMounted(() => {
  return () => {
    if (autoSaveTimer.value) {
      clearTimeout(autoSaveTimer.value)
    }
  }
})
</script>

<style scoped>
.checklist-review {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 优化后的顶部导航栏样式 */
.review-header {
  background: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
}

.title-section h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.title-section .subtitle {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-tag {
  font-weight: 500;
}

.auto-save-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #67c23a;
  font-size: 12px;
}

.saving-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.save-btn, .complete-btn {
  padding: 10px 20px;
  font-weight: 500;
  border-radius: 6px;
}

.header {
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-left h1 {
  margin: 0 0 0 10px;
  font-size: 20px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

/* 优化后的进度统计样式 */
.progress-overview {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.progress-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.progress-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  transition: all 0.3s;
}

.progress-card.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.progress-card.completed {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.progress-card.passed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.progress-card.failed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.progress-card.skipped {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.card-icon {
  font-size: 24px;
  opacity: 0.9;
}

.card-content {
  flex: 1;
}

.card-number {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
}

.card-label {
  font-size: 12px;
  opacity: 0.9;
  margin-top: 4px;
}

.progress-bar-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-text {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.progress-percentage {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
}

.custom-progress {
  margin-top: 8px;
}

.loading-container,
.error-container,
.no-review-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.review-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 优化后的检查项列表样式 */
.items-section {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.table-view {
  /* 表格视图样式 */
  padding: 0;
  border-radius: 0;
}

.items-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafbfc;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.items-count {
  background: #e1f3ff;
  color: #409eff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.header-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-input {
  width: 280px;
}

.status-filter {
  width: 140px;
}

.items-container {
  max-height: 600px;
  overflow-y: auto;
}

.review-items {
  display: flex;
  flex-direction: column;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #606266;
}

.empty-hint {
  font-size: 14px;
  color: #909399;
}

.progress-card {
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.progress-header h3 {
  margin: 0;
  color: #303133;
}

.progress-stats {
  display: flex;
  gap: 24px;
}

.items-card {
  flex: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.review-items {
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .header {
    padding: 0 12px;
  }
  
  .header-left h1 {
    font-size: 16px;
  }
  
  .header-right {
    gap: 8px;
  }
  
  .main-content {
    padding: 12px;
  }
  
  .progress-stats {
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .header-actions {
    justify-content: space-between;
  }
  

}

/* Animation for smooth transitions */
.review-item {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
