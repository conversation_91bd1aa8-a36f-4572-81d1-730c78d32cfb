<template>
  <div class="multi-reviewer-manager">
    <div class="manager-header">
      <div class="header-title">
        <h3>多人协作评审</h3>
        <el-badge :value="collaborators.length" type="success" />
      </div>
      
      <div class="header-actions">
        <el-button type="primary" @click="showAddCollaboratorDialog = true">
          <el-icon><Plus /></el-icon>
          添加评审人
        </el-button>
        
        <el-button @click="showAssignmentDialog = true" :disabled="collaborators.length < 2">
          <el-icon><Connection /></el-icon>
          分配任务
        </el-button>
      </div>
    </div>

    <div class="collaborators-section">
      <div class="section-title">
        <h4>评审人员</h4>
        <span class="section-subtitle">当前共 {{ collaborators.length }} 人参与评审</span>
      </div>
      
      <div class="collaborators-list">
        <div
          v-for="collaborator in collaborators"
          :key="collaborator.id"
          :class="['collaborator-card', { 'current-user': collaborator.id === currentUser }]"
        >
          <div class="collaborator-info">
            <el-avatar :size="40" :src="collaborator.avatar">
              {{ collaborator.name.charAt(0) }}
            </el-avatar>
            
            <div class="collaborator-details">
              <div class="collaborator-name">
                {{ collaborator.name }}
                <el-tag v-if="collaborator.id === currentUser" type="primary" size="small">当前用户</el-tag>
                <el-tag v-if="collaborator.role === 'lead'" type="warning" size="small">主评审人</el-tag>
              </div>
              <div class="collaborator-meta">
                <span class="join-time">加入时间: {{ formatTime(collaborator.joinTime) }}</span>
                <span class="progress">进度: {{ collaborator.progress || 0 }}%</span>
              </div>
            </div>
          </div>
          
          <div class="collaborator-stats">
            <div class="stat-item">
              <span class="stat-label">已评审</span>
              <span class="stat-value">{{ collaborator.reviewedCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">分配项</span>
              <span class="stat-value">{{ collaborator.assignedCount || 0 }}</span>
            </div>
          </div>
          
          <div class="collaborator-actions">
            <el-dropdown @command="handleCollaboratorAction">
              <el-button type="text">
                <el-icon><More /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`view-${collaborator.id}`">查看详情</el-dropdown-item>
                  <el-dropdown-item :command="`assign-${collaborator.id}`">分配任务</el-dropdown-item>
                  <el-dropdown-item 
                    v-if="collaborator.id !== currentUser && collaborator.role !== 'lead'"
                    :command="`remove-${collaborator.id}`"
                    divided
                  >
                    移除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <div class="assignment-section">
      <div class="section-title">
        <h4>任务分配</h4>
        <span class="section-subtitle">检查项分配情况</span>
      </div>
      
      <div class="assignment-overview">
        <el-table :data="assignmentSummary" stripe>
          <el-table-column prop="category" label="分类" width="120" />
          <el-table-column prop="totalItems" label="总数" width="80" />
          <el-table-column prop="assignedItems" label="已分配" width="80" />
          <el-table-column prop="completedItems" label="已完成" width="80" />
          <el-table-column prop="assignees" label="分配给">
            <template #default="{ row }">
              <div class="assignees-list">
                <el-tag
                  v-for="assignee in row.assignees"
                  :key="assignee"
                  size="small"
                  type="info"
                >
                  {{ getCollaboratorName(assignee) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 添加评审人对话框 -->
    <el-dialog
      v-model="showAddCollaboratorDialog"
      title="添加评审人"
      width="500px"
    >
      <el-form :model="addCollaboratorForm" label-width="80px">
        <el-form-item label="用户名">
          <el-input v-model="addCollaboratorForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="addCollaboratorForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="addCollaboratorForm.role" placeholder="选择角色">
            <el-option label="普通评审人" value="reviewer" />
            <el-option label="主评审人" value="lead" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddCollaboratorDialog = false">取消</el-button>
          <el-button type="primary" @click="addCollaborator">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 任务分配对话框 -->
    <el-dialog
      v-model="showAssignmentDialog"
      title="任务分配"
      width="800px"
    >
      <div class="assignment-dialog-content">
        <div class="assignment-rules">
          <h4>分配规则</h4>
          <el-radio-group v-model="assignmentRule">
            <el-radio value="manual">手动分配</el-radio>
            <el-radio value="auto-equal">平均分配</el-radio>
            <el-radio value="auto-category">按分类分配</el-radio>
          </el-radio-group>
        </div>
        
        <div v-if="assignmentRule === 'manual'" class="manual-assignment">
          <div class="assignment-preview">
            <h5>手动分配预览</h5>
            <p>您可以拖拽检查项到不同的评审人进行分配</p>
            <div class="assignment-grid">
              <div class="unassigned-items">
                <h6>未分配项目</h6>
                <div class="items-list">
                  <div v-for="item in unassignedItems" :key="item.id" class="assignment-item">
                    {{ item.sequence }}. {{ item.content }}
                  </div>
                </div>
              </div>
              <div class="reviewer-columns">
                <div v-for="reviewer in collaborators" :key="reviewer.id" class="reviewer-column">
                  <h6>{{ reviewer.name }}</h6>
                  <div class="assigned-items">
                    <div v-for="item in getAssignedItems(reviewer.id)" :key="item.id" class="assignment-item">
                      {{ item.sequence }}. {{ item.content }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="assignmentRule === 'auto-equal'" class="auto-equal-assignment">
          <div class="assignment-preview">
            <h5>平均分配预览</h5>
            <p>将 {{ totalItems }} 个检查项平均分配给 {{ collaborators.length }} 个评审人</p>
            <div class="equal-distribution">
              <div v-for="(reviewer, index) in collaborators" :key="reviewer.id" class="reviewer-allocation">
                <div class="reviewer-info">
                  <strong>{{ reviewer.name }}</strong>
                  <span class="allocation-count">分配 {{ getEqualAllocationCount(index) }} 项</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="assignmentRule === 'auto-category'" class="auto-category-assignment">
          <div class="assignment-preview">
            <h5>按分类分配预览</h5>
            <p>根据评审人的专业领域自动分配对应分类的检查项</p>
            <div class="category-distribution">
              <div v-for="category in categories" :key="category" class="category-allocation">
                <div class="category-info">
                  <strong>{{ category }}</strong>
                  <span class="item-count">{{ getCategoryItemCount(category) }} 项</span>
                </div>
                <div class="assigned-reviewers">
                  <el-tag
                    v-for="reviewer in getReviewersForCategory(category)"
                    :key="reviewer.id"
                    size="small"
                    type="info"
                  >
                    {{ reviewer.name }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAssignmentDialog = false">取消</el-button>
          <el-button type="primary" @click="executeAssignment">执行分配</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Plus, Connection, More } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props
interface Props {
  reviewId: string
  collaborators: any[]
  currentUser: string
}

const props = withDefaults(defineProps<Props>(), {
  collaborators: () => [],
  currentUser: 'current-user'
})

// Emits
interface Emits {
  (e: 'collaborator-add', collaborator: any): void
  (e: 'collaborator-remove', collaboratorId: string): void
  (e: 'assignment-change', assignment: any): void
}

const emit = defineEmits<Emits>()

// Reactive data
const showAddCollaboratorDialog = ref(false)
const showAssignmentDialog = ref(false)
const assignmentRule = ref('manual')

const addCollaboratorForm = ref({
  username: '',
  name: '',
  role: 'reviewer'
})

// Computed
const assignmentSummary = computed(() => {
  // 这里应该根据实际的检查项和分配情况计算
  return [
    {
      category: '代码规范',
      totalItems: 3,
      assignedItems: 2,
      completedItems: 1,
      assignees: ['user1', 'user2']
    },
    {
      category: '文档',
      totalItems: 2,
      assignedItems: 2,
      completedItems: 0,
      assignees: ['user1']
    },
    {
      category: '测试',
      totalItems: 2,
      assignedItems: 1,
      completedItems: 1,
      assignees: ['user2']
    }
  ]
})

// 任务分配相关的计算属性
const totalItems = computed(() => 8) // 这里应该从实际数据计算

const unassignedItems = computed(() => {
  // 返回未分配的检查项
  return [
    { id: 'item1', sequence: 1, content: '代码规范检查', category: '代码规范' },
    { id: 'item2', sequence: 2, content: '注释完整性', category: '文档' }
  ]
})

const categories = computed(() => {
  // 返回所有分类
  return ['代码规范', '文档', '测试', '架构设计']
})

// Methods
const formatTime = (time: string) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleDateString('zh-CN')
}

const getCollaboratorName = (collaboratorId: string) => {
  const collaborator = props.collaborators.find(c => c.id === collaboratorId)
  return collaborator?.name || collaboratorId
}

const handleCollaboratorAction = async (command: string) => {
  const [action, collaboratorId] = command.split('-')
  
  switch (action) {
    case 'view':
      // 查看评审人详情
      break
    case 'assign':
      // 分配任务给特定评审人
      break
    case 'remove':
      try {
        await ElMessageBox.confirm('确定要移除此评审人吗？', '确认操作', {
          type: 'warning'
        })
        emit('collaborator-remove', collaboratorId)
        ElMessage.success('评审人已移除')
      } catch {
        // User cancelled
      }
      break
  }
}

const addCollaborator = () => {
  if (!addCollaboratorForm.value.username || !addCollaboratorForm.value.name) {
    ElMessage.warning('请填写完整信息')
    return
  }
  
  const newCollaborator = {
    id: `user_${Date.now()}`,
    username: addCollaboratorForm.value.username,
    name: addCollaboratorForm.value.name,
    role: addCollaboratorForm.value.role,
    joinTime: new Date().toISOString(),
    progress: 0,
    reviewedCount: 0,
    assignedCount: 0
  }
  
  emit('collaborator-add', newCollaborator)
  showAddCollaboratorDialog.value = false
  
  // 重置表单
  addCollaboratorForm.value = {
    username: '',
    name: '',
    role: 'reviewer'
  }
  
  ElMessage.success('评审人添加成功')
}

const executeAssignment = () => {
  const assignment = {
    rule: assignmentRule.value,
    reviewId: props.reviewId,
    collaborators: props.collaborators
  }

  emit('assignment-change', assignment)
  showAssignmentDialog.value = false
  ElMessage.success('任务分配完成')
}

// 任务分配相关方法
const getAssignedItems = (reviewerId: string) => {
  // 返回分配给特定评审人的检查项
  return []
}

const getEqualAllocationCount = (reviewerIndex: number) => {
  const itemsPerReviewer = Math.floor(totalItems.value / props.collaborators.length)
  const remainder = totalItems.value % props.collaborators.length
  return itemsPerReviewer + (reviewerIndex < remainder ? 1 : 0)
}

const getCategoryItemCount = (category: string) => {
  // 返回特定分类的检查项数量
  const counts: Record<string, number> = {
    '代码规范': 3,
    '文档': 2,
    '测试': 2,
    '架构设计': 1
  }
  return counts[category] || 0
}

const getReviewersForCategory = (category: string) => {
  // 返回负责特定分类的评审人
  return props.collaborators.filter(reviewer =>
    reviewer.assignedCategories && reviewer.assignedCategories.includes(category)
  )
}
</script>

<style scoped>
.multi-reviewer-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.section-title {
  margin-bottom: 16px;
}

.section-title h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.section-subtitle {
  font-size: 12px;
  color: #909399;
}

.collaborators-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
}

.collaborator-card {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s;
}

.collaborator-card:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.collaborator-card.current-user {
  border-color: #409eff;
  background: #f0f9ff;
}

.collaborator-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.collaborator-details {
  flex: 1;
}

.collaborator-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.collaborator-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 16px;
}

.collaborator-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
}

.assignment-overview {
  margin-top: 16px;
}

.assignees-list {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.assignment-dialog-content {
  padding: 16px 0;
}

.assignment-rules {
  margin-bottom: 24px;
}

.assignment-rules h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

/* 任务分配预览样式 */
.assignment-preview {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.assignment-preview h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.assignment-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 16px;
  margin-top: 16px;
}

.unassigned-items,
.reviewer-columns {
  background: #fff;
  border-radius: 6px;
  padding: 12px;
}

.reviewer-columns {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.reviewer-column {
  background: #f0f9ff;
  border-radius: 6px;
  padding: 8px;
}

.reviewer-column h6 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 600;
  color: #409eff;
}

.assignment-item {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 4px;
  font-size: 12px;
  cursor: move;
}

.assignment-item:hover {
  border-color: #409eff;
}

.equal-distribution,
.category-distribution {
  margin-top: 16px;
}

.reviewer-allocation,
.category-allocation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fff;
  border-radius: 6px;
  margin-bottom: 8px;
}

.allocation-count,
.item-count {
  font-size: 12px;
  color: #909399;
}

.assigned-reviewers {
  display: flex;
  gap: 4px;
}
</style>
